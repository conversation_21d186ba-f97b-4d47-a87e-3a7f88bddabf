# 🤖 Multi-Agent RAG System

A sophisticated multi-agent Retrieval-Augmented Generation (RAG) system that combines specialized AI agents for different types of queries. Built with LangGraph, LangChain, and Gradio.

## 🌟 Features

### Specialized Agents
- **🔍 Web Search Agent**: Uses DuckDuckGo to find current information and facts
- **👁️ Visual QA Agent**: Answers questions about uploaded images using Ollama vision models
- **📄 RAG Agent**: Processes PDF documents and answers questions using vector search
- **🧠 Supervisor Agent**: Intelligently routes queries to the appropriate specialized agent

### Models
- **Text Processing**: Ollama Qwen2 (0.5B parameter model)
- **Visual Processing**: Ollama Qwen2-VL (3B parameter model for vision-language tasks)

### Technologies
- **LangGraph**: Agent orchestration and workflow management
- **<PERSON><PERSON>hain**: Components and integrations
- **Gradio**: User-friendly web interface
- **ChromaDB**: Vector database for document embeddings
- **Sentence Transformers**: Text embeddings

## 🚀 Quick Start

### Prerequisites
1. **Python 3.8+** installed
2. **Ollama** installed and running locally
3. Required Ollama models downloaded

### 1. Install Ollama and Models

```bash
# Install Ollama (if not already installed)
# Visit: https://ollama.ai/download

# Pull required models
ollama pull qwen3:0.6b
ollama pull qwen2-vl:3b
```

### 2. Setup Python Environment

```bash
# Clone or navigate to the project directory
cd "multimodel rag"

# Activate virtual environment (if exists)
# Windows:
venv\Scripts\activate
# Linux/Mac:
# source venv/bin/activate

# Install dependencies
pip install -r requirements.txt
```

### 3. Run the Application

```bash
python app.py
```

The application will start and be available at `http://localhost:7860`

## 📁 Project Structure

```
multimodel rag/
├── agents/
│   ├── __init__.py
│   ├── supervisor.py      # Main supervisor agent
│   ├── search_agent.py    # Web search functionality
│   ├── visual_agent.py    # Image processing and QA
│   └── text_agent.py      # PDF RAG functionality
├── utils/
│   ├── __init__.py
│   ├── pdf_processor.py   # PDF text extraction
│   └── vector_store.py    # Vector database management
├── config.py              # Configuration settings
├── app.py                 # Main Gradio application
├── requirements.txt       # Python dependencies
└── README.md             # This file
```

## 🎯 Usage Guide

### 1. Web Search Queries
Ask questions that require current information:
```
"What's the latest news about AI?"
"Current weather in New York"
"Recent developments in quantum computing"
```

### 2. Visual Question Answering
Upload an image and ask questions:
```
"What objects do you see in this image?"
"What color is the car in the picture?"
"Can you read the text in this image?"
```

### 3. Document RAG
Upload PDF documents and ask questions:
```
"What is the main topic of this document?"
"Summarize the key findings"
"What does the document say about [specific topic]?"
```

### 4. General Conversation
Regular chat and questions:
```
"Hello, how are you?"
"Explain machine learning"
"What can you help me with?"
```

## ⚙️ Configuration

Edit `config.py` to customize:

- **Model settings**: Change Ollama models or parameters
- **Vector store**: Adjust embedding models and chunk sizes
- **File uploads**: Modify size limits and allowed extensions
- **Interface**: Change port, host, or UI settings

## 🔧 Advanced Features

### Agent Routing
The supervisor agent automatically determines which specialized agent should handle each query based on:
- Content analysis
- File uploads (images/PDFs)
- Query intent recognition

### Vector Search
- Automatic text chunking and embedding
- Similarity search with relevance scoring
- Persistent storage with ChromaDB

### Error Handling
- Graceful error recovery
- Detailed logging
- User-friendly error messages

## 🐛 Troubleshooting

### Common Issues

1. **Ollama Connection Error**
   - Ensure Ollama is running: `ollama serve`
   - Check if models are downloaded: `ollama list`

2. **Model Not Found**
   - Pull required models: `ollama pull qwen3:0.6b`

3. **Port Already in Use**
   - Change port in `config.py` or kill existing process

4. **Memory Issues**
   - Reduce chunk size in vector store configuration
   - Use smaller models if available

### Logs
Check console output for detailed error messages and debugging information.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is open source. Please check the license file for details.

## 🙏 Acknowledgments

- **Ollama** for local LLM hosting
- **LangChain** for agent frameworks
- **Gradio** for the web interface
- **ChromaDB** for vector storage
