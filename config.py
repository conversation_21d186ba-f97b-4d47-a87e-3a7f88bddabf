"""
Configuration settings for the multi-agent RAG system
"""
import os
from typing import Dict, Any

# Ollama Model Configuration
OLLAMA_CONFIG = {
    "text_model": "qwen3:0.6b",  # Qwen3 0.6B parameter model
    "vision_model": "qwen2-vl:3b",  # Qwen2.5VL 3B parameter model
    "base_url": "http://localhost:11434",
    "temperature": 0.1,
    "top_p": 0.9,
}

# Vector Store Configuration
VECTOR_STORE_CONFIG = {
    "collection_name": "multimodal_rag",
    "persist_directory": "./chroma_db",
    "embedding_model": "all-MiniLM-L6-v2",
    "chunk_size": 1000,
    "chunk_overlap": 200,
}

# Agent Configuration
AGENT_CONFIG = {
    "max_iterations": 10,
    "max_execution_time": 300,  # 5 minutes
    "verbose": True,
}

# Gradio Configuration
GRADIO_CONFIG = {
    "server_name": "0.0.0.0",
    "server_port": 7860,
    "share": False,
    "debug": True,
}

# File Upload Configuration
UPLOAD_CONFIG = {
    "max_file_size": 50 * 1024 * 1024,  # 50MB
    "allowed_image_extensions": [".jpg", ".jpeg", ".png", ".bmp", ".gif"],
    "allowed_pdf_extensions": [".pdf"],
    "upload_directory": "./uploads",
}

# Search Configuration
SEARCH_CONFIG = {
    "max_results": 5,
    "region": "us-en",
    "safesearch": "moderate",
}

def get_config() -> Dict[str, Any]:
    """Get all configuration settings"""
    return {
        "ollama": OLLAMA_CONFIG,
        "vector_store": VECTOR_STORE_CONFIG,
        "agent": AGENT_CONFIG,
        "gradio": GRADIO_CONFIG,
        "upload": UPLOAD_CONFIG,
        "search": SEARCH_CONFIG,
    }

def ensure_directories():
    """Ensure all required directories exist"""
    directories = [
        VECTOR_STORE_CONFIG["persist_directory"],
        UPLOAD_CONFIG["upload_directory"],
        "./agents",
        "./utils",
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
