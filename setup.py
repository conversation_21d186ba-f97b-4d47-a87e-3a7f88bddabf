"""
Setup script for the multi-agent RAG system
"""
import subprocess
import sys
import os
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def run_command(command, description):
    """Run a command and handle errors"""
    try:
        logger.info(f"Running: {description}")
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        logger.info(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"❌ {description} failed: {e}")
        logger.error(f"Error output: {e.stderr}")
        return False

def check_ollama():
    """Check if Ollama is installed and running"""
    try:
        result = subprocess.run("ollama --version", shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            logger.info("✅ Ollama is installed")
            return True
        else:
            logger.error("❌ Ollama is not installed")
            return False
    except:
        logger.error("❌ Ollama is not installed")
        return False

def check_models():
    """Check if required models are available"""
    try:
        result = subprocess.run("ollama list", shell=True, capture_output=True, text=True)
        models = result.stdout
        
        required_models = ["qwen3:0.6b", "qwen2-vl:3b"]
        missing_models = []
        
        for model in required_models:
            if model not in models:
                missing_models.append(model)
        
        if missing_models:
            logger.warning(f"⚠️ Missing models: {missing_models}")
            return False
        else:
            logger.info("✅ All required models are available")
            return True
    except:
        logger.error("❌ Could not check Ollama models")
        return False

def install_dependencies():
    """Install Python dependencies"""
    return run_command(
        f"{sys.executable} -m pip install -r requirements.txt",
        "Installing Python dependencies"
    )

def pull_models():
    """Pull required Ollama models"""
    models = ["qwen3:0.6b", "qwen2-vl:3b"]
    success = True
    
    for model in models:
        if not run_command(f"ollama pull {model}", f"Pulling model {model}"):
            success = False
    
    return success

def setup_directories():
    """Create necessary directories"""
    from config import ensure_directories
    try:
        ensure_directories()
        logger.info("✅ Directories created successfully")
        return True
    except Exception as e:
        logger.error(f"❌ Error creating directories: {e}")
        return False

def main():
    """Main setup function"""
    logger.info("🚀 Starting Multi-Agent RAG System Setup")
    
    # Check Ollama installation
    if not check_ollama():
        logger.error("Please install Ollama first: https://ollama.ai/download")
        return False
    
    # Install Python dependencies
    if not install_dependencies():
        logger.error("Failed to install Python dependencies")
        return False
    
    # Setup directories
    if not setup_directories():
        logger.error("Failed to setup directories")
        return False
    
    # Check and pull models
    if not check_models():
        logger.info("Pulling required models...")
        if not pull_models():
            logger.error("Failed to pull some models")
            return False
    
    logger.info("🎉 Setup completed successfully!")
    logger.info("Run 'python app.py' to start the application")
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
